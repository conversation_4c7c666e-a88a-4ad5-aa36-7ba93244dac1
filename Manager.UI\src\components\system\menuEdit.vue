<template>
	<el-dialog width="50%" v-loading="loading" destroy-on-close v-model="dialog.show" :title="dialog.title">
		<el-form :rules="rules" ref="form" :model="menuModel" label-width="120px">
			<el-row>
				<el-col :span="12">
					<el-form-item label="菜单名称" prop="menuName">
						<el-input v-model="menuModel.menuName" placeholder="菜单名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="图标" prop="icon">
						<div class="icon-selector">
							<el-popover
								ref="iconPopover"
								placement="bottom-start"
								:width="500"
								trigger="click"
								popper-class="icon-popover"
								v-model:visible="iconPopoverVisible"
							>
								<template #reference>
									<div class="icon-input">
										<el-icon v-if="menuModel.icon" class="selected-icon">
											<component :is="getIconComponent(menuModel.icon)" />
										</el-icon>
										<span class="icon-text">{{ getIconDisplayName(menuModel.icon) || '请选择图标' }}</span>
										<el-icon class="arrow-icon">
											<arrow-down />
										</el-icon>
									</div>
								</template>

								<div class="icon-picker-content">
									<div class="icon-search">
										<el-input
											v-model="iconSearchText"
											placeholder="搜索图标..."
											clearable
											size="small"
										>
											<template #prefix>
												<el-icon><search /></el-icon>
											</template>
										</el-input>
									</div>

									<div class="icon-list">
										<div
											v-for="icon in filteredIcons"
											:key="icon.component"
											class="icon-item"
											:class="{ 'selected': menuModel.icon === icon.component }"
											@click="selectIcon(icon.component)"
											:title="icon.name"
										>
											<el-icon class="icon-display">
												<component :is="getIconComponent(icon.component)" />
											</el-icon>
											<span class="icon-name">{{ icon.name }}</span>
										</div>
									</div>

									<div class="icon-actions">
										<el-button size="small" @click="clearIcon">清除</el-button>
										<el-button size="small" type="primary" @click="closeIconPicker">确定</el-button>
									</div>
								</div>
							</el-popover>
						</div>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="排序" prop="sort">
						<el-input v-model="menuModel.sort" placeholder="排序"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="路径名" prop="path">
						<el-input v-model="menuModel.path" placeholder="路径名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="权限" prop="permission">
						<el-input v-model="menuModel.permission" placeholder="权限"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="组件路径" prop="componentPath">
						<el-input v-model="menuModel.componentPath" placeholder="组件路径"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="类型" prop="menuType">
						<el-select style="width: 100%;" v-model="menuModel.menuType" clearable placeholder="类型">
							<el-option v-for="item in typeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="应用类型" prop="appType">
						<el-select style="width: 100%;" v-model="menuModel.appType" clearable placeholder="应用类型">
							<el-option v-for="item in appTypeList" :key="item.nameEn" :label="item.nameCn"
								:value="item.nameEn"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12">
					<el-form-item label="上级" prop="parentId">
						<el-select v-model="menuModel.parentId" placeholder="请选择上级菜单" style="width: 100%" clearable filterable>
							<el-option label="顶级菜单" :value="0"></el-option>
							<el-option
								v-for="menu in parentMenuOptions"
								:key="menu.id"
								:label="menu.menuName"
								:value="menu.id"
								:disabled="menu.id === menuModel.id"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<!-- 空列，保持布局平衡 -->
				</el-col>
			</el-row>
			<el-row>
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input type="textarea" :rows="2" v-model="menuModel.remark" placeholder="备注内容"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<el-row justify="center">
			<el-button type="primary" style="width: 100px;height: 30px;margin-top: 20px;" @click="onSubmit">提
				交</el-button>
		</el-row>
	</el-dialog>
</template>

<script>
import { addMenu, editMenu, loadMenu } from "@/api/system/menu";
import mitt from "@/utils/mitt";
import { elementPlusIcons, getIconComponent } from '@/utils/icon';
import { ArrowDown, Search } from '@element-plus/icons-vue';
export default {
	components: {
		ArrowDown,
		Search
	},
	props: ['statusList', 'typeList', 'appTypeList'],
	data() {
		return {
			loading: false,
			menuModel: {},
			dialog: {},
			parentMenuOptions: [], // 上级菜单选项
			iconList: [], // 图标列表
			iconSearchText: '', // 图标搜索文本
			iconPopoverVisible: false, // 图标弹窗显示状态
			rules: {
				menuName: [{
					required: true,
					message: '请输入菜单名',
					trigger: 'blur',
				}],
				type: [{
					required: true,
					message: '请选择类型',
					trigger: 'change',
				}]
			}
		}
	},
	computed: {
		/**
		 * 过滤后的图标列表
		 */
		filteredIcons() {
			if (!this.iconSearchText) {
				return this.iconList
			}
			return this.iconList.filter(icon =>
				icon.name.toLowerCase().includes(this.iconSearchText.toLowerCase()) ||
				icon.component.toLowerCase().includes(this.iconSearchText.toLowerCase())
			)
		}
	},

	methods: {
		onSubmit() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.menuModel.id == 0) {
						addMenu(this.menuModel)
							.then(() => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					} else {
						editMenu(this.menuModel)
							.then(() => {
								this.$message.success("操作成功")
								this.$emit("search")
								this.dialog.show = false
							})
							.catch(err => {
								this.$message.error(err.data.errorMessage);
							})
					}

				}
			})
		},

		/**
		 * 加载菜单列表用于上级选择
		 */
		loadMenuList() {
			loadMenu()
				.then(res => {
					// 筛选出menuType='menu'的菜单项
					const allMenus = res.data.data || []
					this.parentMenuOptions = allMenus.filter(menu => menu.menuType === 'menu')
				})
				.catch(err => {
					console.error('加载菜单列表失败:', err)
					this.parentMenuOptions = []
				})
		},

		/**
		 * 选择图标
		 */
		selectIcon(iconComponent) {
			this.menuModel.icon = iconComponent
			// 选择图标后自动关闭弹窗
			this.iconPopoverVisible = false
		},

		/**
		 * 清除图标
		 */
		clearIcon() {
			this.menuModel.icon = ''
			// 清除图标后关闭弹窗
			this.iconPopoverVisible = false
		},

		/**
		 * 关闭图标选择器
		 */
		closeIconPicker() {
			// 手动关闭图标弹窗
			this.iconPopoverVisible = false
		},

		/**
		 * 获取图标组件
		 */
		getIconComponent(iconName) {
			return getIconComponent(iconName)
		},

		/**
		 * 获取图标显示名称
		 */
		getIconDisplayName(iconComponent) {
			if (!iconComponent) return ''
			const icon = this.iconList.find(item => item.component === iconComponent)
			return icon ? icon.name : iconComponent
		}
	},
	created() {
		// 初始化图标列表 - Element Plus 方法
		this.iconList = elementPlusIcons
	},
	mounted() {
		// 加载菜单列表
		this.loadMenuList()
		mitt.on('openMenuEdit', (menu) => {
			this.menuModel = menu
			this.dialog.show = true
			this.dialog.title = "修改信息"
		})
		mitt.on('openMenuAdd', (id) => {
			this.menuModel = {
				id: 0,
				parentId: id,
				appType: 'manage' // 默认设置为管理端
			}
			this.dialog.show = true
			this.dialog.title = "添加菜单"
		})
	}
}
</script>

<style scoped>
.menu-edit-form :deep(.el-form-item) {
	margin-bottom: 12px !important;
}
/* Element Plus 图标选择器样式 */
.icon-selector {
  width: 100%;
}

.icon-input {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s;
  background-color: #fff;
  min-height: 32px;
}

.icon-input:hover {
  border-color: #c0c4cc;
}

.icon-input:focus-within {
  border-color: #409eff;
}

.selected-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.icon-text {
  flex: 1;
  color: #606266;
  font-size: 14px;
}

.arrow-icon {
  margin-left: 8px;
  font-size: 12px;
  color: #c0c4cc;
  transition: transform 0.2s;
}

.icon-picker-content {
  padding: 0;
}

.icon-search {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.icon-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 4px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #fff;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item.selected {
  border-color: #409eff;
  background-color: #409eff;
  color: #fff;
}

.icon-display {
  font-size: 20px;
  margin-bottom: 4px;
}

.icon-item.selected .icon-display {
  color: #fff;
}

.icon-name {
  font-size: 10px;
  text-align: center;
  line-height: 1.2;
  word-break: break-all;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.icon-actions {
  padding: 12px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 自定义popover样式 */
:deep(.icon-popover) {
  padding: 0 !important;
}

:deep(.icon-popover .el-popover__arrow) {
  display: none;
}
</style>